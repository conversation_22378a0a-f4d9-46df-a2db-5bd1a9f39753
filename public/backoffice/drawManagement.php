<?php

use models\composite\oDrawManagement\DrawRequestManager;
global $LMRId;

$drawRequestManager = new DrawRequestManager($LMRId);
$requestData = $drawRequestManager->getCategoriesDataArray();

?>
<style>
.work-table {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
}

.work-table thead {
    background: #f3f6f9;
}

.work-table th {
    font-size: 1rem !important;
    color: #495057;
    border: none;
    text-transform: capitalize;
}

.work-table td {
    padding: 1rem 0.75rem;
    border: none;
}

.work-table tbody tr:hover {
    background-color: #f8f9ff;
    transition: all 0.2s ease;
}

.category-header {
    background: #e1f0ff;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    padding: 0.75rem 1rem;
}

.line-item {
    border-bottom: 1px solid #ebedf3 !important;
}

.line-item td:first-child {
    font-weight: 600;
}

.percentage {
    font-weight: 600;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    color: #fff;
    display: inline-block;
    width: 45px;
    text-align: center;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
}

.tooltipClass {
    font-size: 1rem;
}

.note-btn {
    display: contents;
    padding: 0 !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.note-btn i {
    padding: 0 !important;
}
</style>
<div class="card card-body p-0">
    <div class="card card-custom card-stretch d-flex p-0 drawManagementCard">
        <div class="card-header card-header-tabs-line bg-gray-100  ">
            <div class="card-title">
                <h3 class="card-label">
                    Draw Management
                </h3>
            </div>
            <div class="card-toolbar ">
                <a href="javascript:void(0);"
                    class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                    data-card-tool="toggle" data-section="drawManagementCard" data-toggle="tooltip" data-placement="top"
                    title="" data-original-title="Toggle Card">
                    <i class="ki ki-arrow-down icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1 d-none" data-card-tool="reload"
                    data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                    <i class="ki ki-reload icon-nm"></i>
                </a>
                <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary d-none" data-card-tool="remove"
                    data-toggle="tooltip" data-placement="top" title="" data-original-title="Remove Card">
                    <i class="ki ki-close icon-nm"></i>
                </a>
            </div>
        </div>

        <div class="card-body p-2">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-end mb-4 mt-2">
                        <button class="btn btn-primary btn-sm">
                            <i class="fas fa-download mr-2"></i>Export Table
                        </button>
                    </div>

                    <div class="work-table">
                        <table class="table table-hover table-bordered table-vertical-center ">
                            <thead>
                                <tr>
                                    <th>Line Item</th>
                                    <th>Description</th>
                                    <th>Total Budget</th>
                                    <th>Completed Renovations</th>
                                    <th>Previously Disbursed</th>
                                    <th>% Completed</th>
                                    <th>% Requested for Draw</th>
                                    <th>Disbursement This Draw</th>
                                    <th style="width: 70px;">Lender Notes</th>
                                    <th style="width: 70px;">Borrower Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="category-header">
                                    <td colspan="10">
                                        SOFT COSTS
                                        <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                            title="Purchase Price + Repairs Needed"></i>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Surveys</td>
                                    <td>Property boundary and topography surveys</td>
                                    <td>$2,500</td>
                                    <td>$1,500</td>
                                    <td>$1,000</td>
                                    <td>
                                        <span class="percentage">0%</span>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control input-sm">
                                    </td>
                                    <td>
                                        <input type="text" class="form-control input-sm">
                                    </td>
                                    <td style="text-align: center;">
                                        <button class="btn note-btn" type="button">
                                            <i class="fas fa-sticky-note tooltipClass"
                                                data-original-title="Purchase Price + Repairs Needed"></i>
                                        </button>
                                    </td>
                                    <td style="text-align: center;">
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Permits</td>
                                    <td>Building permits and fees</td>
                                    <td>$3,000</td>
                                    <td>$3,000</td>
                                    <td>$2,500</td>
                                    <td>
                                        <span class="percentage">100%</span>
                                    </td>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>

                                <tr class="category-header">
                                    <td colspan="10">FOUNDATION</td>
                                </tr>
                                <tr class="line-item">
                                    <td>Slab</td>
                                    <td>Concrete slab foundation installation</td>
                                    <td>$12,000</td>
                                    <td>$10,000</td>
                                    <td>$8,000</td>
                                    <td>
                                        <span class="percentage">83%</span>
                                    </td>
                                    <td>10</td>
                                    <td></td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Crawl Space</td>
                                    <td>Crawl space prep and construction</td>
                                    <td>$8,000</td>
                                    <td>$5,000</td>
                                    <td>$4,000</td>
                                    <td>
                                        <span class="percentage">62%</span>
                                    </td>
                                    <td>15</td>
                                    <td></td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Footers</td>
                                    <td>Concrete footers for load bearing walls</td>
                                    <td>$4,500</td>
                                    <td>$4,000</td>
                                    <td>$3,500</td>
                                    <td>
                                        <span class="percentage">89%</span>
                                    </td>
                                    <td>5</td>
                                    <td>225</td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>

                                <tr class="category-header">
                                    <td colspan="10">EXTERIOR</td>
                                </tr>
                                <tr class="line-item">
                                    <td>Roof</td>
                                    <td>New asphalt shingle roof installation</td>
                                    <td>$14,000</td>
                                    <td>$10,000</td>
                                    <td>$9,000</td>
                                    <td>
                                        <span class="percentage">71%</span>
                                    </td>
                                    <td>10</td>
                                    <td></td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Paint (Exterior)</td>
                                    <td>Full exterior painting and caulking</td>
                                    <td>$5,000</td>
                                    <td>$1,000</td>
                                    <td>$800</td>
                                    <td>
                                        <span class="percentage">20%</span>
                                    </td>
                                    <td>10</td>
                                    <td>500</td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>

                                <tr class="category-header">
                                    <td colspan="10">INTERIOR</td>
                                </tr>
                                <tr class="line-item">
                                    <td>Drywall</td>
                                    <td>Install and finish drywall throughout house</td>
                                    <td>$7,000</td>
                                    <td>$2,000</td>
                                    <td>$1,500</td>
                                    <td>
                                        <span class="percentage">29%</span>
                                    </td>
                                    <td>10</td>
                                    <td>700</td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Paint (Interior)</td>
                                    <td>Interior wall and ceiling painting</td>
                                    <td>$4,000</td>
                                    <td>$1,500</td>
                                    <td>$1,000</td>
                                    <td>
                                        <span class="percentage">38%</span>
                                    </td>
                                    <td>10</td>
                                    <td>400</td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Bathroom Tile</td>
                                    <td>Tile installation in bathrooms</td>
                                    <td>$3,500</td>
                                    <td>$2,200</td>
                                    <td>$1,800</td>
                                    <td>
                                        <span class="percentage">63%</span>
                                    </td>
                                    <td>15</td>
                                    <td>525</td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                                <tr class="line-item">
                                    <td>Kitchen Cabinets</td>
                                    <td>Install upper and lower kitchen cabinets</td>
                                    <td>$8,000</td>
                                    <td>$3,000</td>
                                    <td>$3,000</td>
                                    <td>
                                        <span class="percentage">38%</span>
                                    </td>
                                    <td>10</td>
                                    <td>800</td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                    <td>
                                        <button class="btn note-btn btn-sm"><i class="fas fa-sticky-note"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Action Buttons -->
<div class="d-flex justify-content-center btn-sm action-buttons">
    <input type="submit" class="btn btn-primary save-btn mr-2" value="Save">
    <input type="submit" class="btn btn-success save-btn" value="Submit for Approval">

</div>

<script>
$(document).ready(function() {
    $('.percentage').each(function() {
        const percentage = parseInt($(this).text());
        $(this).css('background', getPercentageColor(percentage));
    });
});

function getPercentageColor(percentage) {
    // Ensure percentage is between 0 and 100
    const p = Math.max(0, Math.min(100, percentage));

    const saturation = 100;
    const lightness = 45;



    // Hue: 0 for red (0% progress), 120 for green (100% progress)
    const hue = (p / 100) * 120;

    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
}
</script>
