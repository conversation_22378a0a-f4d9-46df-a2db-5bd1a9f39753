/**
 * Borrower-specific Draw Management Functionality
 */

// Extend DrawManagement with borrower-specific functionality
DrawManagement.borrower = {

    /**
     * Initialize borrower-specific functionality
     */
    init: function() {
        DrawManagement.config.lmrid = $('#lmrid').val();
        DrawManagement.config.dataKey = 'lmrid';
        DrawManagement.config.dataUrl = `/backoffice/api_v2/DrawManagement/borrower/SowCategories?lmrid=${DrawManagement.config.lmrid}`;
        DrawManagement.config.saveLineItemsSuccessMessage = 'Draw Request Submitted!';
        this.initEventHandlers();
    },

    /**
     * Initialize borrower-specific event handlers
     */
    initEventHandlers: function() {
        const self = this;

        // Notes modal handlers
        this.initNotesModal();

        // Input change handlers for cost, completion fields
        DrawManagement.elements.$lineItemCategoriesContainer.on('input', 'input[placeholder="0.00"], input[placeholder="0"]', function() {
            DrawManagement.config.lineItemsModified = true;

            // Get the input element and its value
            const $input = $(this);
            let value = parseFloat($input.val()) || 0;

            // Ensure value is not negative
            if (value < 0) {
                value = 0;
                $input.val(value);
            }

            // Round to 2 decimal places
            value = Math.round(value * 100) / 100;

            // Field-specific validations
            const fieldName = $input.attr('name');
            const $row = $input.closest('tr');
            const cost = parseFloat($row.find('input[name="cost"]').val()) || 0;

            // If cost field changed, keep percentage and update amount
            if (fieldName === 'cost') {
                const percent = parseFloat($row.find('input[name="completedPercent"]').val()) || 0;
                const amount = Math.round((percent / 100) * value * 100) / 100;
                $row.find('input[name="completedAmount"]').val(amount);
            }

            if (fieldName === 'completedAmount') {
                // Ensure completedAmount doesn't exceed cost
                if (value > cost) {
                    value = cost;
                    $input.val(value);
                }
                // Auto-update percentage field
                const percentage = cost > 0 ? Math.round((value / cost) * 100) : 0;
                $row.find('input[name="completedPercent"]').val(percentage);
            }
            else if (fieldName === 'completedPercent') {
                // Ensure percentage doesn't exceed 100
                if (value > 100) {
                    value = 100;
                    $input.val(value);
                }
                // Auto-update amount field
                const amount = Math.round((value / 100) * cost * 100) / 100;
                $row.find('input[name="completedAmount"]').val(amount);
            }

            // Update the value in case it was modified by validation
            $input.val(value);
        });

        let hoverTimeout;
        let isClickInProgress = false;

        DrawManagement.elements.$lineItemCategoriesContainer
            .on('mousedown', '.note-btn', function() {
                isClickInProgress = true;
                clearTimeout(hoverTimeout);
                $(this).closest('.note-container').find('.popover').stop(true, true).hide();
            })
            .on('mouseup', '.note-btn', function() {
                setTimeout(() => {
                    isClickInProgress = false;
                }, 0);
            })
            .on('click', '.note-btn', function(e) {
                $(this).closest('.note-container').find('.popover').stop(true, true).hide();
            })
            .on('mouseover', '.note-btn', function() {
                if (isClickInProgress) {
                    return;
                }

                const $btn = $(this);
                const $notesContainer = $btn.closest('.note-container');
                const $popover = $notesContainer.find('.popover');
                clearTimeout(hoverTimeout);

                hoverTimeout = setTimeout(function() {
                    if (isClickInProgress) {
                        $popover.stop(true, true).hide();
                        return;
                    }

                    const note = $btn.data('note');
                    if (note && note.trim()) {
                        $('.popover', DrawManagement.elements.$lineItemCategoriesContainer).not($popover).stop(true, true).hide();
                        $popover.text(note);
                        $popover.stop(true, true).fadeIn(100);
                    } else {
                        $popover.stop(true, true).hide();
                    }
            }, 500);
        })
        .on('mouseleave', '.note-btn', function() {
            clearTimeout(hoverTimeout);
            isClickInProgress = false;
            $(this).closest('.note-container').find('.popover').stop(true, true).fadeOut(100);
        });
    },

    /**
     * Initialize notes modal functionality
     */
    initNotesModal: function() {
        let currentNoteBtn;

        $('#noteModal').on('show.bs.modal', function (event) {
            currentNoteBtn = $(event.relatedTarget);
            const noteText = currentNoteBtn.data('note') || '';
            $('#noteTextarea').val(noteText);
        });

        $('#saveNoteBtn').on('click', function () {
            const updatedNote = $('#noteTextarea').val();
            currentNoteBtn.data('note', updatedNote);
            currentNoteBtn.attr('data-note', updatedNote);
            $('#noteModal').modal('hide');
            DrawManagement.config.lineItemsModified = true;
            DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
        });
    },

    /**
     * Build categories JSON for saving
     * @returns {Array} - Categories data
     */
    buildCategoriesJson: function() {
        var categories = [];
        DrawManagement.elements.$categoriesContainer.find('.category-item').each(function(index) {
            var $this = $(this);
            var id = $this.data('category-id');
            var name = $this.find('.category-name').text();
            var description = $this.find('.category-description').text();
            var order = index + 1;

            if (typeof id === 'string' && id.startsWith('new_cat_')) {
                id = null;
            } else {
                id = parseInt(id, 10);
            }
            categories.push({ id: id, name: name, description: description, order: order });
        });

        return {
            lmrid: DrawManagement.config.lmrid,
            categories: categories
        };
    },

    /**
     * Build line items JSON for saving
     */
    buildLineItemsJson: function() {
        const groupedLineItems = {};

        DrawManagement.elements.$lineItemCategoriesContainer.find('.line-item-category-section').each(function() {
            const $categoryCard = $(this);
            const categoryId = $categoryCard.data('category-id');
            if (!categoryId) {
                return;
            }

            const itemsForThisCategory = [];
            $categoryCard.find('tbody.sortable tr.editable-line-item').each(function(index) {
                const $row = $(this);
                let lineItemId = $row.data('line-item-id');
                let name, description, cost, completedAmount, completedPercent, notes;

                // For new items, get data from inputs or data attributes if they were blurred
                if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_')) {
                    name = $row.find('.line-item-name-input').val().trim() || $row.data('new-name');
                    description = '';
                    if (!name) {
                        return;
                    }
                    lineItemId = null;
                } else {
                    name = $row.find('.line-item-name-display').text().trim();
                    lineItemId = parseInt(lineItemId, 10);
                }

                // Extract borrower-specific fields
                cost = parseFloat($row.find('input[name="cost"]').val()) || 0;
                completedAmount = parseFloat($row.find('input[name="completedAmount"]').val()) || 0;
                completedPercent = parseFloat($row.find('input[name="completedPercent"]').val()) || 0;
                notes = $row.find('.note-btn:not(.description-btn)').data('note') || '';
                description = $row.find('.description-btn').data('note') || '';

                itemsForThisCategory.push({
                    id: lineItemId,
                    categoryId: categoryId,
                    name: name,
                    description: description,
                    cost: cost,
                    completedAmount: completedAmount,
                    completedPercent: completedPercent,
                    notes: notes,
                    order: index + 1
                });
            });
            if (itemsForThisCategory.length === 0) {
                groupedLineItems[categoryId] = [];
            }
            groupedLineItems[categoryId] = itemsForThisCategory;
        });

        return {
            lmrid: DrawManagement.config.lmrid,
            lineItems: groupedLineItems,
            saveDraft: DrawManagement.config.saveDraft
        };
    },

    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');
                $row.attr('data-line-item-id', item.id);

                // Set line item name
                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);
                $row.find('.line-item-name-display').attr('title', item.description);
                $row.find('.line-item-description-input').val(item.description);

                // Set borrower-specific fields
                $row.find('input[name="cost"]').val(item.cost || 0);
                $row.find('input[name="completedAmount"]').val(item.completedAmount || 0);
                $row.find('input[name="completedPercent"]').val(item.completedPercent || 0);

                // Set notes data attribute
                const popoverElems = $row.find('.note-btn');

                popoverElems.each(function() {
                    const $popoverElem = $(this);
                    const isDescriptionBtn = $popoverElem.hasClass('description-btn');
                    const note = isDescriptionBtn ? item.description : item.notes;
                    if (note) {
                        $popoverElem.data('note', note);
                        $popoverElem.attr('data-note', note);
                    } else {
                        $popoverElem.data('note', '');
                    }
                });

                $tbodyElement.append($row);
            });
        }
    }
};

